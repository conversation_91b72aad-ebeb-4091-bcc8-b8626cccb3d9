// ARCHIVED ON: 2025-08-08T12:14:58.424Z
// REASON: Unused script cleanup
// ORIGINAL PATH: scripts/fix-shift-sync-improved.js

#!/usr/bin/env node

const { Pool } = require('pg');
require('dotenv').config();

// Constants
const SHIFT_STATUS = {
    SCHEDULED: 'scheduled',
    ACTIVE: 'active',
    COMPLETED: 'completed'
};

const QUERIES = {
    FIND_PROBLEMATIC_SHIFTS: `
        SELECT id, truck_id, shift_type, status, start_date, end_date, start_time, end_time
        FROM driver_shifts 
        WHERE status = $1 AND end_date >= CURRENT_DATE
        ORDER BY truck_id, start_time
    `,
    RESET_SHIFTS: `
        UPDATE driver_shifts 
        SET status = $1, updated_at = CURRENT_TIMESTAMP 
        WHERE status = $2 AND end_date >= CURRENT_DATE
    `,
    GET_UPDATED_SHIFTS: `
        SELECT 
            id, truck_id, shift_type, status, start_date, end_date, start_time, end_time,
            evaluate_shift_status(id, CURRENT_TIMESTAMP) as calculated_status
        FROM driver_shifts 
        WHERE id = ANY($1)
        ORDER BY truck_id, start_time
    `,
    GET_TIME_CONTEXT: `
        SELECT 
            CURRENT_DATE as current_date,
            CURRENT_TIME as current_time,
            CURRENT_TIMESTAMP as current_timestamp
    `,
    GET_TEST_SHIFTS: `
        SELECT id, shift_type 
        FROM driver_shifts 
        WHERE shift_type IN ('day', 'night') 
        AND status != 'completed'
        LIMIT 2
    `
};

class ShiftSyncService {
    constructor() {
        this.pool = new Pool({
            user: process.env.DB_USER,
            host: process.env.DB_HOST,
            database: process.env.DB_NAME,
            password: process.env.DB_PASSWORD,
            port: process.env.DB_PORT,
        });
    }

    async validateEnvironment() {
        const requiredEnvVars = ['DB_USER', 'DB_HOST', 'DB_NAME', 'DB_PASSWORD', 'DB_PORT'];
        const missing = requiredEnvVars.filter(env => !process.env[env]);
        
        if (missing.length > 0) {
            throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
        }
    }

    async findProblematicShifts() {
        const result = await this.pool.query(QUERIES.FIND_PROBLEMATIC_SHIFTS, [SHIFT_STATUS.COMPLETED]);
        
        console.log('🔍 Found incorrectly completed shifts:');
        result.rows.forEach(shift => {
            console.log(`   ID ${shift.id}: Truck ${shift.truck_id} (${shift.shift_type}) - ends ${shift.end_date.toISOString().substring(0,10)}`);
        });
        
        return result;
    }

    async resetShiftsToScheduled() {
        const resetResult = await this.pool.query(QUERIES.RESET_SHIFTS, [SHIFT_STATUS.SCHEDULED, SHIFT_STATUS.COMPLETED]);
        console.log(`✅ Reset ${resetResult.rowCount} shifts to '${SHIFT_STATUS.SCHEDULED}' status`);
        return resetResult;
    }

    async runAutoActivation() {
        await this.pool.query('SELECT schedule_auto_activation()');
        console.log('✅ Ran auto-activation with corrected logic');
    }

    async validateResults(problemShiftIds) {
        const updatedShifts = await this.pool.query(QUERIES.GET_UPDATED_SHIFTS, [problemShiftIds]);
        
        console.log('\n📊 Updated Shift Status:');
        console.log('   ID  | Truck | Type  | Status    | Should Be | Time Range | Date Range');
        console.log('   ----|-------|-------|-----------|-----------|------------|------------');
        
        updatedShifts.rows.forEach(shift => {
            console.log(`   ${this.formatShiftRow(shift)}`);
        });
        
        return updatedShifts;
    }

    formatShiftRow(shift) {
        const statusMatch = shift.status === shift.calculated_status ? '✅' : '⚠️';
        const timeRange = `${shift.start_time.substring(0,5)}-${shift.end_time.substring(0,5)}`;
        const dateRange = `${shift.start_date.toISOString().substring(5,10)} to ${shift.end_date.toISOString().substring(5,10)}`;
        
        return [
            shift.id.toString().padStart(3),
            `DT-${shift.truck_id.toString().padStart(3)}`,
            shift.shift_type.padEnd(5),
            shift.status.padEnd(9),
            `${shift.calculated_status.padEnd(9)} ${statusMatch}`,
            timeRange.padEnd(10),
            dateRange
        ].join(' | ');
    }

    async displayTimeContext() {
        const timeContext = await this.pool.query(QUERIES.GET_TIME_CONTEXT);
        const ctx = timeContext.rows[0];
        
        console.log('\n🕐 Current Time Context:');
        console.log(`   Date: ${ctx.current_date.toISOString().substring(0,10)}`);
        console.log(`   Time: ${ctx.current_time.substring(0,8)}`);
    }

    async runLogicVerification() {
        console.log('\n🧪 Logic Verification:');
        
        try {
            const testShifts = await this.pool.query(QUERIES.GET_TEST_SHIFTS);
            
            if (testShifts.rows.length < 2) {
                console.log('   ⚠️ Insufficient test data available');
                return;
            }

            const dayShift = testShifts.rows.find(s => s.shift_type === 'day');
            const nightShift = testShifts.rows.find(s => s.shift_type === 'night');

            if (dayShift) {
                const dayResult = await this.pool.query(
                    `SELECT evaluate_shift_status($1, CURRENT_DATE + '12:00:00'::TIME) as result`,
                    [dayShift.id]
                );
                console.log(`   Day shift at 12:00 should be active: ${dayResult.rows[0].result}`);
            }

            if (nightShift) {
                const nightResult = await this.pool.query(
                    `SELECT evaluate_shift_status($1, CURRENT_DATE + '23:00:00'::TIME) as result`,
                    [nightShift.id]
                );
                console.log(`   Night shift at 23:00 should be active: ${nightResult.rows[0].result}`);
            }
        } catch (error) {
            console.log(`   ⚠️ Logic verification failed: ${error.message}`);
        }
    }

    async execute() {
        console.log('🔧 Fixing Shift Status Synchronization...\n');
        
        try {
            await this.validateEnvironment();
            
            const problemShifts = await this.findProblematicShifts();
            const problemShiftIds = problemShifts.rows.map(s => s.id);
            
            if (problemShiftIds.length === 0) {
                console.log('✅ No problematic shifts found. System is already synchronized.');
                return;
            }
            
            await this.resetShiftsToScheduled();
            await this.runAutoActivation();
            await this.validateResults(problemShiftIds);
            await this.displayTimeContext();
            await this.runLogicVerification();
            
            console.log('\n🎉 Shift synchronization completed!');
            
        } catch (error) {
            this.handleError(error);
        } finally {
            await this.cleanup();
        }
    }

    handleError(error) {
        if (error.code === '23505') {
            console.error('❌ Database constraint violation:', error.detail);
        } else if (error.code === 'ECONNREFUSED') {
            console.error('❌ Database connection failed. Check if PostgreSQL is running.');
        } else if (error.code === '42P01') {
            console.error('❌ Database table not found. Run migrations first.');
        } else if (error.code === '42883') {
            console.error('❌ Database function not found. Check if all migrations have been applied.');
        } else {
            console.error('❌ Unexpected error:', error.message);
            if (process.env.NODE_ENV === 'development') {
                console.error('Stack trace:', error.stack);
            }
        }
        process.exit(1);
    }

    async cleanup() {
        await this.pool.end();
    }
}

// Main execution
async function fixShiftSync() {
    const service = new ShiftSyncService();
    await service.execute();
}

if (require.main === module) {
    fixShiftSync();
}

module.exports = { fixShiftSync, ShiftSyncService };