# Archived Scripts Index
Generated on: 2025-08-08T12:14:59.326Z

## Reason for Archival
These scripts were one-time fixes or standalone utilities that are no longer needed for the core system operation.

## Archived Files
- comprehensive-shift-fix.js - One-time fix/utility script
- final-fix.js - One-time fix/utility script
- final-shift-fix.js - One-time fix/utility script
- final-shift-status-fix.js - One-time fix/utility script
- final-status-fix.js - One-time fix/utility script
- fix-function-conflict.js - One-time fix/utility script
- fix-shift-sync.js - One-time fix/utility script
- fix-shift-sync-improved.js - One-time fix/utility script
- create-auto-activation.js - One-time fix/utility script
- verify-ports.js - One-time fix/utility script

## Restoration
To restore any script, copy it back to the scripts directory:
```bash
cp archive/comprehensive-shift-fix.js ./
```

## Safety
All archived scripts have been verified as unused by the core system:
- No imports found in server or client code
- No references in package.json scripts  
- No exported functions used elsewhere
