import React, { useState, useCallback, useMemo } from 'react';
import { Scanner } from '@yudiel/react-qr-scanner';
import toast from 'react-hot-toast';
import { getDriverStatus, processDriverConnect } from '../../services/driverAPI';
import { driverConnectOffline } from '../../services/driverConnectOffline';
import { offlineShiftState } from '../../services/offlineShiftState';
import { usePWAStatus } from '../../hooks/usePWAStatus';
import { useAudioFeedback } from '../../hooks/useAudioFeedback';
import { useQRScanner } from '../../hooks/useQRScanner';
import { useDeviceDetection } from '../../hooks/useDeviceDetection';
import { validateQRData } from '../../utils/qrValidation';
import DriverStatusErrorModal, { DriverStatusIndicator } from '../../components/DriverStatusErrorModal';
import { driverStatusCache } from '../../services/offlineDB';

// Constants
const SCAN_STEPS = {
  DRIVER: 'driver',
  TRUCK: 'truck'
};

const RESET_DELAY = 3000;
const SCAN_DEBOUNCE_DELAY = 2000;
const TOAST_DURATIONS = {
  DEFAULT: 4000,
  SUCCESS: 5000,
  EXTENDED: 6000
};

const DriverConnect = () => {
  // Scanner states
  const [scanStep, setScanStep] = useState(SCAN_STEPS.DRIVER);
  const [driverData, setDriverData] = useState(null);
  const [currentShift, setCurrentShift] = useState(null);

  // PHASE 4: Manual action selection state for offline mode
  const [manualAction, setManualAction] = useState('');
  const [showManualSelection, setShowManualSelection] = useState(false);
  const [pendingTruckScan, setPendingTruckScan] = useState(null); // Store pending truck scan data

  // Driver status validation states
  const [statusError, setStatusError] = useState(null);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [driverStatus, setDriverStatus] = useState(null);
  const [statusLastUpdated, setStatusLastUpdated] = useState(null);

  // Custom hooks
  const {
    isScanning,
    cameraError,
    facingMode,
    loading,
    setLoading,
    lastScanRef,
    scanTimeoutRef,
    refreshCamera,
    toggleCamera,
    switchCamera,
    handleScanError: handleCameraError
  } = useQRScanner();

  const { isMobile, isLandscape, getCameraDimensions } = useDeviceDetection();

  // PWA and offline state using centralized hook
  const {
    isOnline,
    syncStatus,
    queuedConnections,
    isPWA,
    triggerSync,
    installPWA,
    canInstall
  } = usePWAStatus();

  // Audio feedback using custom hook
  const { playSuccessSound, playErrorSound } = useAudioFeedback();

  // PHASE 4: Manage manual action selection visibility for offline mode
  React.useEffect(() => {
    // Show manual selection when offline and ready for truck scan
    const shouldShowManualSelection = !isOnline && scanStep === SCAN_STEPS.TRUCK && driverData;

    console.log('[DriverConnect] Manual selection visibility check:', {
      isOnline,
      scanStep,
      hasDriverData: !!driverData,
      shouldShowManualSelection,
      currentManualAction: manualAction
    });

    setShowManualSelection(shouldShowManualSelection);

    // FIXED: Only reset manual action when going back to driver step or going online
    // Don't clear it just because manual selection UI is hidden
    if (scanStep === SCAN_STEPS.DRIVER || isOnline) {
      console.log('[DriverConnect] Clearing manual action due to step/mode change');
      setManualAction('');
      setPendingTruckScan(null);
    }
  }, [isOnline, scanStep, driverData, manualAction]);



  // Reset scanner state
  const resetScanner = useCallback(() => {
    setDriverData(null);
    setCurrentShift(null);
    setScanStep(SCAN_STEPS.DRIVER);
    setManualAction('');
    setShowManualSelection(false);
    setPendingTruckScan(null);
    toast.success('Scanner reset - ready for driver ID scan');
  }, []);





  // PWA Installation and Network Monitoring is now handled by usePWAStatus hook

  // Get driver status using imported API function with status validation handling
  const fetchDriverStatus = useCallback(async (employeeId) => {
    try {
      // If offline, try to get cached status first
      if (!isOnline) {
        const cachedStatus = await driverStatusCache.getCachedDriverStatus(employeeId);
        if (cachedStatus) {
          console.log(`[DriverConnect] Using cached driver status for ${employeeId}:`, cachedStatus);

          // Update driver status state for display
          setDriverStatus({
            status: cachedStatus.status,
            statusDisplayName: cachedStatus.statusDisplayName,
            driver: cachedStatus.driver
          });
          setStatusLastUpdated(cachedStatus.cachedAt);

          return {
            success: cachedStatus.isValid,
            driver: cachedStatus.driver,
            driver_status: cachedStatus.status,
            status_display_name: cachedStatus.statusDisplayName,
            status: 'checked_out', // Default for offline
            status_message: 'Offline mode - using cached status',
            cached_at: cachedStatus.cachedAt,
            cache_expiry: cachedStatus.expiresAt,
            offline_mode: true
          };
        }

        // No cached status available in offline mode
        console.log(`[DriverConnect] No cached status for ${employeeId} in offline mode`);
        return {
          success: true, // Allow offline operation
          driver: {
            employee_id: employeeId,
            full_name: `Driver ${employeeId}`,
            status: 'unknown'
          },
          driver_status: 'unknown',
          status_display_name: 'Unknown (Offline)',
          status: 'checked_out',
          status_message: 'Offline mode - no cached status',
          offline_mode: true
        };
      }

      // Online mode: fetch from server
      const response = await getDriverStatus(employeeId);
      const data = response.data;

      // Cache the status for offline use
      if (data.success) {
        await driverStatusCache.cacheDriverStatus(employeeId, {
          status: data.driver_status,
          statusDisplayName: data.status_display_name,
          driver: data.driver,
          message: data.status_message,
          success: data.success
        });
      }

      // Update driver status state for display
      if (data.success) {
        setDriverStatus({
          status: data.driver_status,
          statusDisplayName: data.status_display_name,
          driver: data.driver
        });
        setStatusLastUpdated(data.cached_at || new Date().toISOString());
      }

      return data;
    } catch (error) {
      console.error('Error getting driver status:', error);

      // Handle status validation errors specifically
      if (error.response && error.response.status === 403) {
        const errorData = error.response.data;
        if (errorData.error === 'DRIVER_STATUS_BLOCKED') {
          // Set status error state to show modal
          setStatusError({
            error: errorData.error,
            message: errorData.message,
            status: errorData.status,
            statusDisplayName: errorData.statusDisplayName,
            driver: errorData.driver
          });
          setShowStatusModal(true);

          // Don't throw error - let the modal handle it
          return {
            success: false,
            error: errorData.error,
            message: errorData.message,
            statusBlocked: true
          };
        }
      }

      throw error;
    }
  }, [isOnline]);

  // Process driver connect using imported API function
  const submitDriverConnect = useCallback(async (driverQrData, truckQrData, action = null) => {
    try {
      const payload = {
        driver_qr_data: driverQrData,
        truck_qr_data: truckQrData
      };

      // Only include action if explicitly provided (for offline sync)
      if (action !== null && action !== undefined) {
        payload.action = action;
      }

      const response = await processDriverConnect(payload);
      return response.data;
    } catch (error) {
      console.error('Error processing driver connect:', error);
      throw error;
    }
  }, []);

  // Store driver connection data offline using enhanced service with API compatibility
  const storeOfflineConnection = useCallback(async (connectionData) => {
    try {
      // Validate connection data before storing
      if (!connectionData.driver_qr_data || !connectionData.truck_qr_data) {
        throw new Error('Invalid connection data: missing required QR data');
      }

      if (!connectionData.action) {
        throw new Error('Invalid connection data: action is required');
      }

      // Sanitize data before storage
      const sanitizedData = {
        action: connectionData.action,
        driver_qr_data: connectionData.driver_qr_data,
        truck_qr_data: connectionData.truck_qr_data,
        timestamp: connectionData.timestamp || new Date().toISOString(),
        scanner_type: 'driver_connect'
      };

      // Use enhanced offline service for API-compatible storage
      const result = await driverConnectOffline.storeConnection(sanitizedData);

      if (result.success) {
        toast.success(result.message || '📱 Connection saved offline - use sync button when online');

        // Auto-sync removed - manual sync only via sync button
        console.log('Connection stored offline. Use sync button to upload when online.');

        return result;
      } else {
        throw new Error(result.message || 'Failed to store connection offline');
      }
    } catch (error) {
      console.error('Failed to store offline connection:', error);
      toast.error(`Failed to save connection offline: ${error.message}`);
      throw error;
    }
  }, []);

  // Centralized error handler
  const handleScanError = useCallback((error, context = 'scan') => {
    playErrorSound();
    console.error(`${context} error:`, error);

    const errorInfo = {
      title: 'Connection Error',
      message: error.response?.data?.message || error.message || 'An unexpected error occurred',
      duration: TOAST_DURATIONS.DEFAULT
    };

    toast.error(
      <div>
        <div className="font-medium">{errorInfo.title}</div>
        <div className="text-sm mt-1">{errorInfo.message}</div>
      </div>,
      { duration: errorInfo.duration }
    );

    refreshCamera();
  }, [playErrorSound, refreshCamera]);



  // Stable authentication bypass function using useCallback with minimal dependencies
  const shouldBypassAuthentication = useCallback(() => {
    return !isOnline;
  }, [isOnline]);

  // Helper function to handle driver scan step
  const handleDriverScanStep = useCallback(async (qrData) => {
    if (qrData.type !== 'driver') {
      throw new Error(`Expected driver QR code, but scanned ${qrData.type}`);
    }

    let statusResponse;

    // Conditional authentication checks that bypass validation during offline mode
    if (shouldBypassAuthentication()) {
      // Offline mode: Create mock response from QR data without server call
      statusResponse = {
        success: true,
        driver: {
          employee_id: qrData.employee_id,
          full_name: qrData.full_name || `Driver ${qrData.employee_id}`,
        },
        status: 'checked_out', // Default to checked_out for offline mode
        status_message: 'Offline mode - driver authenticated locally',
        shift_info: null, // No shift info available offline
        current_truck: null
      };

      console.log('[DriverConnect] Using offline authentication bypass for:', qrData.employee_id);
    } else {
      // Online mode: Use normal authentication flow
      try {
        statusResponse = await fetchDriverStatus(qrData.employee_id);

        if (!statusResponse.success) {
          // Check if this is a status blocked error
          if (statusResponse.statusBlocked) {
            // Status modal is already shown by fetchDriverStatus
            // Stop the scan process here
            return;
          }
          throw new Error(statusResponse.message || 'Failed to get driver status');
        }
      } catch (error) {
        // If online but API fails, fall back to offline mode
        console.log('[DriverConnect] API failed while online, falling back to offline mode:', error.message);
        statusResponse = {
          success: true,
          driver: {
            employee_id: qrData.employee_id,
            full_name: qrData.full_name || `Driver ${qrData.employee_id}`,
          },
          status: 'checked_out',
          status_message: 'API unavailable - using offline mode',
          shift_info: null,
          current_truck: null
        };
      }
    }

    playSuccessSound();
    setDriverData({
      employee_id: statusResponse.driver.employee_id,
      full_name: statusResponse.driver.full_name,
      qr_data: qrData
    });

    setCurrentShift(statusResponse.shift_info);

    // ENHANCED: Store active shift state for offline mode if driver is checked in
    if (statusResponse.status === 'checked_in' && statusResponse.shift_info && statusResponse.current_truck) {
      try {
        await offlineShiftState.storeActiveShift({
          employeeId: statusResponse.driver.employee_id,
          driverName: statusResponse.driver.full_name,
          truckId: statusResponse.current_truck.id?.toString() || statusResponse.current_truck.truck_number,
          truckNumber: statusResponse.current_truck.truck_number,
          shiftId: statusResponse.shift_info.id,
          checkInTime: statusResponse.shift_info.created_at || new Date().toISOString(),
          source: 'online'
        });
        console.log('[DriverConnect] Active shift stored for offline mode');
      } catch (error) {
        console.error('[DriverConnect] Failed to store active shift for offline mode:', error);
      }
    } else if (statusResponse.status === 'checked_out') {
      // Ensure no stale shift data in offline storage
      try {
        await offlineShiftState.removeActiveShift(statusResponse.driver.employee_id);
      } catch (error) {
        console.error('[DriverConnect] Failed to clear offline shift state:', error);
      }
    }

    // Determine next step message
    let nextStepMessage;
    if (statusResponse.status === 'checked_out') {
      nextStepMessage = 'Step 2: Scan Truck QR Code to CHECK IN';
    } else {
      nextStepMessage = `Step 2: Scan ${statusResponse.current_truck?.truck_number || 'Truck'} QR Code to CHECK OUT`;
    }

    // Show appropriate message based on mode
    const modeIndicator = shouldBypassAuthentication() ? '📱 ' : '';
    const statusMessage = statusResponse.status_message || 'Ready to proceed';

    toast.success(
      <div>
        <div className="font-medium">{modeIndicator}Welcome, {statusResponse.driver.full_name}!</div>
        <div className="text-sm mt-1">{statusMessage}</div>
        <div className="text-sm mt-1 text-blue-600">{nextStepMessage}</div>
      </div>,
      { duration: TOAST_DURATIONS.DEFAULT }
    );

    setScanStep(SCAN_STEPS.TRUCK);
  }, [fetchDriverStatus, playSuccessSound, shouldBypassAuthentication]);

  // Status refresh handler
  const handleStatusRefresh = useCallback(async () => {
    if (!driverData?.employee_id) {
      toast.error('No driver selected to refresh status');
      return;
    }

    try {
      setLoading(true);
      await fetchDriverStatus(driverData.employee_id);
      toast.success('Driver status refreshed');
    } catch (error) {
      console.error('Failed to refresh driver status:', error);
      toast.error('Failed to refresh driver status');
    } finally {
      setLoading(false);
    }
  }, [driverData?.employee_id, fetchDriverStatus, setLoading]);

  // Modal handlers
  const handleStatusModalClose = useCallback(() => {
    setShowStatusModal(false);
    setStatusError(null);
  }, []);

  const handleStatusModalRetry = useCallback(() => {
    handleStatusRefresh();
  }, [handleStatusRefresh]);

  // Helper function to handle truck scan step
  const handleTruckScanStep = useCallback(async (qrData) => {
    if (qrData.type !== 'truck') {
      throw new Error(`Expected truck QR code, but scanned ${qrData.type}`);
    }

    let connectResponse;

    try {
      if (isOnline) {
        // REVERTED: Original online mode - let server determine action automatically
        connectResponse = await submitDriverConnect(
          driverData.qr_data,
          qrData
        );

        // Update offline shift state based on successful online action (simplified)
        if (connectResponse.success) {
          if (connectResponse.action === 'check_in' || connectResponse.action === 'handover') {
            await offlineShiftState.storeActiveShift({
              employeeId: driverData.qr_data.employee_id,
              driverName: driverData.full_name,
              truckId: qrData.id || qrData.truck_number,
              truckNumber: qrData.truck_number || qrData.id,
              shiftId: connectResponse.shift_id || `online-${Date.now()}`,
              checkInTime: connectResponse.check_in_time || new Date().toISOString(),
              source: 'online'
            });
          } else if (connectResponse.action === 'check_out') {
            await offlineShiftState.removeActiveShift(driverData.qr_data.employee_id);
          }
        }
      } else {
        throw new Error('Offline mode - storing connection for later sync');
      }
    } catch (connectionError) {
      console.error('Connection Error or offline mode:', connectionError);

      // PHASE 4: Use manual action selection for offline mode instead of intelligent detection
      let action, actionResult;

      if (!isOnline) {
        // OFFLINE MODE: Use manual action selection
        if (!manualAction) {
          throw new Error('Manual action selection required for offline mode');
        }

        action = manualAction;
        actionResult = {
          action: manualAction,
          reason: 'manual_selection',
          message: `Manual ${manualAction.replace('_', ' ')} selected`,
          source: 'manual'
        };
        console.log('[DriverConnect] Manual offline action selected:', actionResult);
      } else {
        // ONLINE MODE: Preserve intelligent action determination (fallback for edge cases)
        const intelligentResult = await offlineShiftState.determineOfflineAction(
          driverData.qr_data.employee_id,
          qrData.id || qrData.truck_number
        );
        action = intelligentResult.action;
        actionResult = intelligentResult;
        console.log('[DriverConnect] Online intelligent action determined:', actionResult);
      }

      await storeOfflineConnection({
        action: action,
        driver_qr_data: driverData.qr_data,
        truck_qr_data: qrData,
        timestamp: new Date().toISOString(),
        scanner_type: 'driver_connect'
      });

      // Update offline shift state based on action
      if (action === 'check_in') {
        await offlineShiftState.storeActiveShift({
          employeeId: driverData.qr_data.employee_id,
          driverName: driverData.full_name,
          truckId: qrData.id || qrData.truck_number,
          truckNumber: qrData.truck_number || qrData.id,
          shiftId: `offline-${Date.now()}`, // Temporary ID for offline shifts
          checkInTime: new Date().toISOString(),
          source: 'offline'
        });
      } else if (action === 'check_out') {
        await offlineShiftState.removeActiveShift(driverData.qr_data.employee_id);
      }

      connectResponse = {
        success: true,
        action: action,
        message: `📱 ${actionResult.message} - will sync when connected`,
        truck: qrData.truck_number || qrData.id,
        check_in_time: new Date().toISOString(),
        check_out_time: action === 'check_out' ? new Date().toISOString() : null,
        offline_mode: true,
        offline_reason: actionResult.reason
      };
    }

    if (!connectResponse.success) {
      throw new Error(connectResponse.message || 'Failed to process connection');
    }

    playSuccessSound();

    // Show success message based on action
    if (connectResponse.action === 'check_in') {
      toast.success(
        <div>
          <div className="font-medium">✅ Checked In Successfully!</div>
          <div className="text-sm mt-1">Truck: {connectResponse.truck}</div>
          <div className="text-sm">Time: {new Date(connectResponse.check_in_time).toLocaleTimeString()}</div>
        </div>,
        { duration: TOAST_DURATIONS.SUCCESS }
      );
    } else if (connectResponse.action === 'check_out') {
      toast.success(
        <div>
          <div className="font-medium">✅ Checked Out Successfully!</div>
          <div className="text-sm mt-1">Truck: {connectResponse.truck}</div>
          <div className="text-sm">Duration: {connectResponse.duration}</div>
          <div className="text-sm">Time: {new Date(connectResponse.check_out_time).toLocaleTimeString()}</div>
        </div>,
        { duration: TOAST_DURATIONS.EXTENDED }
      );
    } else if (connectResponse.action === 'handover') {
      toast.success(
        <div>
          <div className="font-medium">🔄 Handover Completed!</div>
          <div className="text-sm mt-1">From: {connectResponse.previous_truck}</div>
          <div className="text-sm">To: {connectResponse.new_truck}</div>
          <div className="text-sm">Time: {new Date(connectResponse.check_in_time).toLocaleTimeString()}</div>
        </div>,
        { duration: TOAST_DURATIONS.EXTENDED }
      );
    }

    // Reset for next driver after a delay
    setTimeout(() => {
      resetScanner();
    }, RESET_DELAY);
  }, [driverData, isOnline, submitDriverConnect, storeOfflineConnection, playSuccessSound, resetScanner, manualAction]);

  // PHASE 4: Auto-process pending truck scan when manual action is selected
  React.useEffect(() => {
    const processPendingScan = async () => {
      if (pendingTruckScan && manualAction && !isOnline && scanStep === SCAN_STEPS.TRUCK) {
        console.log('[DriverConnect] Auto-processing pending truck scan with manual action:', {
          manualAction,
          pendingTruckScan
        });

        try {
          setLoading(true);
          await handleTruckScanStep(pendingTruckScan);
          setPendingTruckScan(null); // Clear pending scan after successful processing
        } catch (error) {
          console.error('[DriverConnect] Failed to process pending truck scan:', error);
          handleScanError(error, 'pending scan processing');
          setPendingTruckScan(null); // Clear pending scan even on error
        } finally {
          setLoading(false);
        }
      }
    };

    processPendingScan();
  }, [manualAction, pendingTruckScan, isOnline, scanStep, handleTruckScanStep, setLoading, handleScanError]);

  // Handle QR code scan with debouncing
  const handleScan = useCallback(async (result) => {
    if (!result || loading) return;

    const data = result[0]?.rawValue;
    if (!data) return;

    // Prevent duplicate scans within 2 seconds
    if (data === lastScanRef.current) return;

    // Clear any pending scan timeout
    if (scanTimeoutRef.current) {
      clearTimeout(scanTimeoutRef.current);
    }

    setLoading(true);
    let scanProcessed = false; // Track if scan was successfully processed

    try {
      // Validate and parse QR data
      const qrData = validateQRData(data);

      // FIXED: Enhanced step validation with better error messages for offline mode
      if (scanStep === SCAN_STEPS.DRIVER) {
        // Validate we're expecting a driver QR code
        if (qrData.type !== 'driver') {
          throw new Error(`Expected driver QR code, but scanned ${qrData.type} QR code. Please scan your driver ID first.`);
        }
        await handleDriverScanStep(qrData);
        scanProcessed = true;
      } else if (scanStep === SCAN_STEPS.TRUCK) {
        // Validate we're expecting a truck QR code
        if (qrData.type !== 'truck') {
          throw new Error(`Expected truck QR code, but scanned ${qrData.type} QR code. Please scan the truck QR code.`);
        }

        // PHASE 4: Handle manual action selection for offline mode
        if (!isOnline) {
          console.log('[DriverConnect] Offline truck scan - checking manual action:', {
            manualAction,
            hasManualAction: !!manualAction,
            showManualSelection
          });

          if (!manualAction) {
            // Store the truck scan data and show manual selection UI
            setPendingTruckScan(qrData);
            toast.success(
              <div>
                <div className="font-medium">🚛 Truck QR Code Scanned!</div>
                <div className="text-sm mt-1">Please select Check In or Check Out below</div>
              </div>,
              { duration: TOAST_DURATIONS.DEFAULT }
            );
            scanProcessed = true; // Mark as processed to prevent re-scanning
            return; // Exit early, don't process the truck scan yet
          }
        }
        await handleTruckScanStep(qrData);
        scanProcessed = true;
      } else {
        // This shouldn't happen, but handle it gracefully
        throw new Error(`Invalid scan step: ${scanStep}. Please reset the scanner.`);
      }

      // Only set lastScanRef and stop camera if scan was successfully processed
      lastScanRef.current = data;
      refreshCamera(); // Stop camera immediately after successful scan

    } catch (error) {
      // For manual action selection errors in offline mode, don't prevent re-scanning
      const isManualActionError = !isOnline && scanStep === SCAN_STEPS.TRUCK && !manualAction;
      
      if (!isManualActionError) {
        // For other errors, set lastScanRef to prevent immediate re-scanning
        lastScanRef.current = data;
        refreshCamera(); // Stop camera for other errors
      }
      
      handleScanError(error, 'scan');
    } finally {
      setLoading(false);

      // Only reset scan debounce if scan was processed successfully
      if (scanProcessed) {
        scanTimeoutRef.current = setTimeout(() => {
          lastScanRef.current = '';
        }, SCAN_DEBOUNCE_DELAY);
      }
    }
  }, [loading, scanStep, handleDriverScanStep, handleTruckScanStep, refreshCamera, handleScanError, lastScanRef, scanTimeoutRef, setLoading, isOnline, manualAction, showManualSelection]);

  // Handle scan error
  const handleError = useCallback((error) => {
    const errorMessage = handleCameraError(error);
    toast.error(errorMessage);
  }, [handleCameraError]);

  // Memoize step instructions to avoid recalculation on every render
  const instructions = useMemo(() => {
    if (scanStep === 'driver') {
      return {
        title: 'Step 1: Scan QR Code on Back of Your ID',
        description: 'Hold your ID card close to the camera (4-8 inches) and scan the QR code on the back.',
        icon: '🆔'
      };
    } else {
      const action = currentShift ? 'CHECK OUT' : 'CHECK IN';
      return {
        title: `Step 2: Scan Truck QR Code to ${action}`,
        description: currentShift
          ? `Scan the QR code on ${currentShift.current_truck?.truck_number || 'your assigned truck'} to check out.`
          : 'Scan the QR code on the truck you want to check in to.',
        icon: '🚛'
      };
    }
  }, [scanStep, currentShift]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className={`font-bold text-gray-900 ${isMobile ? 'text-xl' : 'text-2xl'}`}>
                🚛 Driver Connect
                {isMobile && (
                  <span className="ml-2 text-sm text-gray-500">
                    {isLandscape ? '🔄 Landscape' : '📱 Portrait'}
                  </span>
                )}
              </h1>
              <p className={`text-gray-600 mt-1 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                Scan your ID and truck QR codes to check in/out
              </p>
            </div>
            <div className="mt-4 sm:mt-0 flex items-center space-x-3">
              {/* Enhanced Network Status & Sync Indicator */}
              <div className="flex items-center space-x-2">
                {/* Primary Network Status - More Prominent */}
                <div className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-semibold border-2 ${isOnline
                  ? 'bg-green-50 text-green-800 border-green-200'
                  : 'bg-red-50 text-red-800 border-red-200'
                  }`}>
                  <div className={`w-3 h-3 rounded-full ${isOnline ? 'bg-green-500 animate-pulse' : 'bg-red-500'
                    }`}></div>
                  <span>{isOnline ? '🌐 Online Mode' : '📱 Offline Mode'}</span>
                </div>

                {/* PWA Mode Indicator - Enhanced */}
                {isPWA && (
                  <div className="bg-blue-50 text-blue-800 border-2 border-blue-200 px-3 py-1 rounded-lg text-xs font-medium">
                    📱 PWA Active
                  </div>
                )}





                {/* PWA Install Button */}
                {!isPWA && canInstall && (
                  <button
                    onClick={async () => {
                      const result = await installPWA();
                      if (result.success) {
                        toast.success('📱 App installed successfully!');
                      } else {
                        toast.error(`Installation failed: ${result.message}`);
                      }
                    }}
                    className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-medium hover:bg-blue-200 transition-colors"
                  >
                    📱 Install App for Offline Mode
                  </button>
                )}

                {/* Non-PWA Message */}
                {!isPWA && !canInstall && (
                  <div className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-xs font-medium">
                    💡 Install as PWA for offline sync
                  </div>
                )}
              </div>


            </div>
          </div>
        </div>
      </div>

      {/* Driver Status Indicator */}
      {driverStatus && (
        <div className="max-w-4xl mx-auto px-4 py-2">
          <DriverStatusIndicator
            status={driverStatus.status}
            statusDisplayName={driverStatus.statusDisplayName}
            lastUpdated={statusLastUpdated}
            isOffline={!isOnline}
            onRefresh={isOnline ? handleStatusRefresh : null}
          />
        </div>
      )}

      <div className="max-w-4xl mx-auto px-4 py-6 space-y-6">
        {/* Step Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center">
            <span className="text-3xl mr-4">{instructions.icon}</span>
            <div>
              <h3 className="text-lg font-medium text-blue-900">{instructions.title}</h3>
              <p className="text-blue-700 mt-1">{instructions.description}</p>
            </div>
          </div>
        </div>

        {/* Offline Mode Banner - Prominent notification when offline */}
        {!isOnline && (
          <div className="bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-300 rounded-lg p-6 shadow-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center">
                    <span className="text-2xl">📱</span>
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-amber-900">
                    🔄 Offline Mode Active
                  </h3>
                  <p className="text-amber-800 mt-1">
                    Your scans are being stored locally. Use the sync button below when back online to transfer data.
                  </p>
                </div>
              </div>
              {isPWA && (
                <div className="flex-shrink-0 ml-4">
                  <div className="bg-amber-100 text-amber-800 px-3 py-1 rounded-full text-sm font-medium">
                    ✅ PWA Ready
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Online Mode Confirmation - Enhanced indicator for clear mode distinction */}
        {isOnline && scanStep === SCAN_STEPS.TRUCK && driverData && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4" data-testid="online-auto-indicator">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                <span className="text-lg">🌐</span>
              </div>
              <div>
                <h4 className="font-medium text-green-900">ONLINE MODE - Automatic Detection</h4>
                <p className="text-green-700 text-sm">System will automatically determine Check In or Check Out based on your current shift status</p>
              </div>
            </div>
          </div>
        )}

        {/* General Online Status - Subtle indicator when online */}
        {isOnline && queuedConnections === 0 && syncStatus === 'synced' && scanStep !== SCAN_STEPS.TRUCK && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                <span className="text-lg">🌐</span>
              </div>
              <div>
                <h4 className="font-medium text-green-900">Online & Synced</h4>
                <p className="text-green-700 text-sm">All data is being saved directly to the server</p>
              </div>
            </div>
          </div>
        )}

        {/* Driver Status */}
        {driverData && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <span className="text-2xl mr-3">👤</span>
              <div>
                <h4 className="font-medium text-green-900">Driver Authenticated</h4>
                <p className="text-green-700 text-sm">
                  {driverData.full_name} ({driverData.employee_id})
                </p>
                {currentShift && (
                  <p className="text-green-600 text-xs mt-1">
                    Current shift: {currentShift.current_truck?.truck_number || 'Active'}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* PHASE 4: Manual Action Selection for Offline Mode */}
        {showManualSelection && (
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4" data-testid="manual-action-selection">
            <div className="flex items-start">
              <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <span className="text-lg">📱</span>
              </div>
              <div className="flex-1">
                <div className="flex items-center mb-2">
                  <h4 className="font-medium text-amber-900" data-testid="offline-mode-indicator">
                    OFFLINE MODE - Manual Action Selection
                  </h4>
                </div>
                <p className="text-amber-700 text-sm mb-4" data-testid="manual-selection-help">
                  Select your intended action before scanning the truck QR code
                </p>

                <div className="space-y-2">
                  <label className="flex items-center p-3 bg-white border border-amber-200 rounded-lg cursor-pointer hover:bg-amber-50 transition-colors">
                    <input
                      type="radio"
                      name="manualAction"
                      value="check_in"
                      checked={manualAction === 'check_in'}
                      onChange={(e) => {
                        console.log('[DriverConnect] Manual action selected:', e.target.value);
                        setManualAction(e.target.value);
                      }}
                      className="mr-3 text-amber-600 focus:ring-amber-500"
                      data-testid="action-check-in"
                    />
                    <div>
                      <div className="font-medium text-gray-900">Check In to Truck</div>
                      <div className="text-gray-600 text-sm">Start your shift with this truck</div>
                    </div>
                  </label>

                  <label className="flex items-center p-3 bg-white border border-amber-200 rounded-lg cursor-pointer hover:bg-amber-50 transition-colors">
                    <input
                      type="radio"
                      name="manualAction"
                      value="check_out"
                      checked={manualAction === 'check_out'}
                      onChange={(e) => {
                        console.log('[DriverConnect] Manual action selected:', e.target.value);
                        setManualAction(e.target.value);
                      }}
                      className="mr-3 text-amber-600 focus:ring-amber-500"
                      data-testid="action-check-out"
                    />
                    <div>
                      <div className="font-medium text-gray-900">Check Out from Truck</div>
                      <div className="text-gray-600 text-sm">End your shift with this truck</div>
                    </div>
                  </label>
                </div>

                {!manualAction && (
                  <div className="mt-3 p-2 bg-amber-100 border border-amber-300 rounded text-amber-800 text-sm" data-testid="action-selection-required">
                    ⚠️ Please select an action before scanning the truck QR code
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Camera Control Buttons - Moved above camera interface */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex flex-col space-y-4">
            <h3 className="text-lg font-medium text-gray-900 text-center">Camera Controls</h3>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={resetScanner}
                className="flex-1 sm:flex-none sm:min-w-[140px] px-6 py-4 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors flex items-center justify-center text-base font-medium"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Reset
              </button>
              <button
                onClick={toggleCamera}
                className={`flex-1 sm:flex-none sm:min-w-[140px] px-6 py-4 rounded-lg transition-colors flex items-center justify-center text-base font-medium ${isScanning
                  ? 'bg-red-500 text-white hover:bg-red-600'
                  : 'bg-blue-500 text-white hover:bg-blue-600'
                  }`}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                {isScanning ? 'Stop Camera' : 'Start Camera'}
              </button>
              {isMobile && (
                <button
                  onClick={switchCamera}
                  className="flex-1 sm:flex-none sm:min-w-[140px] px-6 py-4 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center text-base font-medium"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                  </svg>
                  Switch Camera
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Camera Section */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-4 border-b">
            <div className="flex items-center justify-center">
              <h3 className="text-lg font-medium text-gray-900">QR Code Scanner</h3>
            </div>
          </div>

          <div className="p-4">
            {cameraError ? (
              <div className="text-center py-12">
                <div className="text-red-500 text-6xl mb-4">📷</div>
                <h3 className="text-lg font-medium text-red-900 mb-2">Camera Error</h3>
                <p className="text-red-700 mb-4">{cameraError}</p>
                <button
                  onClick={toggleCamera}
                  className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                >
                  Try Again
                </button>
              </div>
            ) : isScanning ? (
              <div className="relative">
                <Scanner
                  onScan={handleScan}
                  onError={handleError}
                  constraints={{
                    facingMode: facingMode,
                    aspectRatio: isMobile ? (isLandscape ? 16 / 9 : 4 / 3) : 16 / 9
                  }}
                  styles={{
                    container: {
                      width: '100%',
                      height: getCameraDimensions(),
                      borderRadius: '8px',
                      overflow: 'hidden'
                    }
                  }}
                />

                {loading && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <div className="bg-white rounded-lg p-4 flex items-center space-x-3">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                      <span className="text-gray-900">Processing...</span>
                    </div>
                  </div>
                )}

                {/* Scanning overlay */}
                <div className="absolute inset-0 pointer-events-none">
                  <div className="absolute inset-0 border-2 border-dashed border-white opacity-50 rounded-lg"></div>
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <div className="w-48 h-48 border-2 border-blue-500 rounded-lg"></div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">📱</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Camera Ready</h3>
                <p className="text-gray-600 mb-4">Click "Start Camera" to begin scanning</p>
                <button
                  onClick={toggleCamera}
                  className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  Start Camera
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Sync Section - Repositioned below QR Code Scanner */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex flex-col items-center space-y-4">
            <h3 className="text-lg font-medium text-gray-900 text-center">Offline Data Sync</h3>
            
            {/* Sync Status Indicators */}
            <div className="flex flex-wrap items-center justify-center gap-3">
              {/* Offline Data Queue Indicator */}
              {queuedConnections > 0 && (
                <div className="bg-amber-50 text-amber-800 border-2 border-amber-200 px-4 py-2 rounded-lg text-sm font-medium animate-pulse">
                  💾 {queuedConnections} stored locally
                </div>
              )}

              {/* Sync Status Indicator */}
              {syncStatus !== 'synced' && (
                <div className={`px-4 py-2 rounded-lg text-sm font-medium border-2 ${syncStatus === 'syncing' ? 'bg-blue-50 text-blue-800 border-blue-200' :
                  syncStatus === 'pending' ? 'bg-yellow-50 text-yellow-800 border-yellow-200' :
                    'bg-red-50 text-red-800 border-red-200'
                  }`}>
                  {syncStatus === 'syncing' ? '🔄 Syncing to server...' :
                    syncStatus === 'pending' ? '⏳ Sync pending' :
                      '❌ Sync failed'}
                </div>
              )}
            </div>

            {/* Enhanced Manual Sync Button - Larger and centered */}
            {(queuedConnections > 0 || syncStatus === 'error') && (
              <button
                onClick={async () => {
                  if (!isOnline) {
                    toast.error('Cannot sync while offline. Please connect to internet first.', {
                      duration: 4000,
                      icon: '📱'
                    });
                    return;
                  }

                  toast.loading('Syncing offline data to server...', { id: 'sync-toast' });
                  const result = await triggerSync();

                  if (result.success) {
                    toast.success(`✅ ${result.message}`, {
                      id: 'sync-toast',
                      duration: 5000
                    });
                  } else {
                    toast.error(`❌ ${result.message}`, {
                      id: 'sync-toast',
                      duration: 6000
                    });
                  }
                }}
                disabled={syncStatus === 'syncing' || !isOnline}
                className={`px-8 py-4 rounded-lg text-lg font-bold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg min-w-[200px] ${isOnline
                  ? 'bg-blue-500 text-white hover:bg-blue-600 hover:shadow-xl transform hover:scale-105'
                  : 'bg-gray-300 text-gray-600'
                  } ${queuedConnections > 0 ? 'animate-pulse' : ''}`}
                title={!isOnline ? 'Connect to internet to sync offline data' : `Sync ${queuedConnections} offline connection${queuedConnections !== 1 ? 's' : ''} to server`}
              >
                {syncStatus === 'syncing' ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Syncing...
                  </span>
                ) : !isOnline ? (
                  '📱 Sync When Online'
                ) : (
                  `🔄 Sync ${queuedConnections > 0 ? queuedConnections : ''} Now`
                )}
              </button>
            )}

            {/* Show message when no sync needed */}
            {queuedConnections === 0 && syncStatus === 'synced' && isOnline && (
              <div className="text-center text-gray-500 text-sm">
                ✅ All data is synced
              </div>
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">How to Use</h3>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">1</span>
              <div>
                <p className="text-gray-900 font-medium">Scan Your Driver ID</p>
                <p className="text-gray-600 text-sm">Hold your ID card 4-8 inches from the camera and scan the QR code on the back</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">2</span>
              <div>
                <p className="text-gray-900 font-medium">
                  {isOnline ? 'Scan Truck QR Code' : 'Select Action & Scan Truck QR Code'}
                </p>
                <p className="text-gray-600 text-sm">
                  {isOnline
                    ? 'Scan the QR code on your assigned truck - system will automatically determine check in or out'
                    : 'When offline: First select Check In or Check Out, then scan the truck QR code'
                  }
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <span className="flex-shrink-0 w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium">✓</span>
              <div>
                <p className="text-gray-900 font-medium">Confirmation</p>
                <p className="text-gray-600 text-sm">You'll see a confirmation message with your check-in/out time and duration</p>
              </div>
            </div>
          </div>
        </div>
      </div>






      {/* Driver Status Error Modal */}
      <DriverStatusErrorModal
        isOpen={showStatusModal}
        onClose={handleStatusModalClose}
        driverStatus={statusError?.status}
        statusDisplayName={statusError?.statusDisplayName}
        message={statusError?.message}
        driverInfo={statusError?.driver}
        onRetry={handleStatusModalRetry}
      />
    </div>
  );
};

export default DriverConnect;