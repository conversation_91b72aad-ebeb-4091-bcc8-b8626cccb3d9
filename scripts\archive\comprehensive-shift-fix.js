// ARCHIVED ON: 2025-08-08T12:14:58.317Z
// REASON: Unused script cleanup
// ORIGINAL PATH: scripts/comprehensive-shift-fix.js

#!/usr/bin/env node

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
});

async function comprehensiveShiftFix() {
    console.log('🔧 Comprehensive Shift System Fix...\n');
    
    try {
        // Step 1: Fix function signatures
        console.log('1. Fixing function signatures...');
        
        await pool.query(`
            DROP FUNCTION IF EXISTS fix_incorrectly_completed_shifts();
            DROP FUNCTION IF EXISTS check_shift_status_consistency();
        `);
        
        await pool.query(`
            CREATE FUNCTION fix_incorrectly_completed_shifts()
            RETURNS TABLE (
                shift_id INTEGER,
                old_status shift_status,
                new_status shift_status,
                truck_id INTEGER,
                driver_id INTEGER,
                shift_type shift_type,
                start_date DATE,
                end_date DATE
            ) AS $$
            DECLARE
                v_shift RECORD;
                v_correct_status TEXT;
                v_fixed_count INTEGER := 0;
            BEGIN
                FOR v_shift IN
                    SELECT ds.id, ds.truck_id, ds.driver_id, ds.shift_type, ds.status, ds.start_date, ds.end_date
                    FROM driver_shifts ds
                    WHERE ds.status = 'completed'
                LOOP
                    v_correct_status := evaluate_shift_status(v_shift.id, CURRENT_TIMESTAMP);
                    
                    IF v_correct_status != v_shift.status::TEXT THEN
                        UPDATE driver_shifts
                        SET status = v_correct_status::shift_status, updated_at = CURRENT_TIMESTAMP
                        WHERE id = v_shift.id;
                        
                        v_fixed_count := v_fixed_count + 1;
                        
                        RETURN QUERY SELECT 
                            v_shift.id, v_shift.status, v_correct_status::shift_status,
                            v_shift.truck_id, v_shift.driver_id, v_shift.shift_type,
                            v_shift.start_date, v_shift.end_date;
                    END IF;
                END LOOP;
                
                RAISE NOTICE 'Fixed % incorrectly completed shifts', v_fixed_count;
            END;
            $$ LANGUAGE plpgsql;
        `);
        
        console.log('✅ Fixed function signatures');
        
        // Step 2: Fix shift statuses
        console.log('2. Fixing shift statuses...');
        
        const fixResult = await pool.query('SELECT * FROM fix_incorrectly_completed_shifts()');
        console.log(`✅ Fixed ${fixResult.rows.length} incorrectly completed shifts`);
        
        // Step 3: Run auto-activation
        await pool.query('SELECT schedule_auto_activation()');
        console.log('✅ Ran auto-activation');
        
        // Step 4: Check current shift status
        const currentShifts = await pool.query(`
            SELECT 
                ds.id, 
                ds.truck_id, 
                ds.driver_id, 
                ds.shift_type, 
                ds.status, 
                dt.truck_number, 
                d.full_name,
                d.employee_id,
                ds.start_time,
                ds.end_time,
                evaluate_shift_status(ds.id, CURRENT_TIMESTAMP) as calculated_status
            FROM driver_shifts ds
            JOIN dump_trucks dt ON ds.truck_id = dt.id
            JOIN drivers d ON ds.driver_id = d.id
            WHERE ds.status != 'cancelled'
            ORDER BY dt.truck_number, ds.shift_type
        `);
        
        console.log('\n📊 Current Shift Status:');
        console.log('   Truck   | Driver        | Type  | Status    | Should Be | Time Range');
        console.log('   --------|---------------|-------|-----------|-----------|------------');
        
        currentShifts.rows.forEach(shift => {
            const statusMatch = shift.status === shift.calculated_status ? '✅' : '⚠️';
            const timeRange = `${shift.start_time.substring(0,5)}-${shift.end_time.substring(0,5)}`;
            
            console.log(`   ${shift.truck_number.padEnd(7)} | ${shift.full_name.substring(0,13).padEnd(13)} | ${shift.shift_type.padEnd(5)} | ${shift.status.padEnd(9)} | ${shift.calculated_status.padEnd(9)} ${statusMatch} | ${timeRange}`);
        });
        
        // Step 5: Test assignment query
        console.log('\n3. Testing assignment query...');
        
        const assignmentTest = await pool.query(`
            SELECT
                a.id, a.assignment_code,
                t.truck_number,
                d.full_name as assigned_driver,
                ds.driver_id as current_shift_driver_id,
                sd.full_name as current_shift_driver_name,
                ds.shift_type as current_shift_type,
                ds.status as shift_status,
                CASE
                    WHEN ds.id IS NOT NULL AND ds.status = 'active' THEN
                        CONCAT('✅ ', ds.shift_type, ' Shift Active')
                    WHEN ds.id IS NOT NULL AND ds.status = 'scheduled' THEN
                        CONCAT('📅 ', ds.shift_type, ' Shift Scheduled')
                    WHEN ds.id IS NOT NULL AND ds.status = 'completed' THEN
                        CONCAT('✅ ', ds.shift_type, ' Shift Completed')
                    ELSE '⚠️ No Active Shift'
                END as active_shift_status
            FROM assignments a
            JOIN dump_trucks t ON a.truck_id = t.id
            LEFT JOIN drivers d ON a.driver_id = d.id
            LEFT JOIN driver_shifts ds ON (
                ds.truck_id = a.truck_id
                AND ds.status = 'active'
                AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
                AND (
                    (ds.end_time < ds.start_time AND
                     (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
                    OR
                    (ds.end_time >= ds.start_time AND
                     CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
                )
            )
            LEFT JOIN drivers sd ON ds.driver_id = sd.id
            ORDER BY t.truck_number
        `);
        
        console.log('\n📋 Assignment Status Test:');
        console.log('   Truck   | Assigned Driver | Active Shift Status');
        console.log('   --------|-----------------|--------------------');
        
        assignmentTest.rows.forEach(assignment => {
            console.log(`   ${assignment.truck_number.padEnd(7)} | ${(assignment.assigned_driver || 'None').substring(0,15).padEnd(15)} | ${assignment.active_shift_status}`);
        });
        
        // Step 6: Check current time context
        const timeContext = await pool.query(`
            SELECT 
                CURRENT_DATE as current_date,
                CURRENT_TIME as current_time,
                EXTRACT(hour FROM CURRENT_TIME) as current_hour
        `);
        
        const ctx = timeContext.rows[0];
        console.log('\n🕐 Current Time Context:');
        console.log(`   Date: ${ctx.current_date.toISOString().substring(0,10)}`);
        console.log(`   Time: ${ctx.current_time.substring(0,8)} (Hour: ${ctx.current_hour})`);
        console.log(`   Expected: Night shifts should be ${ctx.current_hour >= 18 || ctx.current_hour < 6 ? 'ACTIVE' : 'SCHEDULED'}`);
        console.log(`   Expected: Day shifts should be ${ctx.current_hour >= 6 && ctx.current_hour < 18 ? 'ACTIVE' : 'SCHEDULED'}`);
        
        console.log('\n🎉 Comprehensive fix completed!');
        console.log('\n📝 Summary:');
        console.log('   ✅ Fixed function signature issues');
        console.log('   ✅ Corrected shift statuses');
        console.log('   ✅ Verified assignment query logic');
        console.log('   ✅ System should now show correct shift statuses');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        console.error('Stack:', error.stack);
    } finally {
        await pool.end();
    }
}

if (require.main === module) {
    comprehensiveShiftFix();
}

module.exports = { comprehensiveShiftFix };