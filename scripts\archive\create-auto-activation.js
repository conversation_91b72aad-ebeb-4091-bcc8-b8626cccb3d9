// ARCHIVED ON: 2025-08-08T12:14:58.570Z
// REASON: Unused script cleanup
// ORIGINAL PATH: scripts/create-auto-activation.js

#!/usr/bin/env node

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
});

async function createAutoActivation() {
    console.log('🔧 Creating auto-activation function...\n');
    
    try {
        await pool.query(`
            CREATE OR REPLACE FUNCTION schedule_auto_activation()
            RETURNS void AS $$
            DECLARE
                rec RECORD;
                new_status TEXT;
                count_activated INTEGER := 0;
                count_completed INTEGER := 0;
            BEGIN
                FOR rec IN SELECT id, status FROM driver_shifts WHERE status != 'cancelled'
                LOOP
                    new_status := evaluate_shift_status(rec.id, CURRENT_TIMESTAMP);
                    IF new_status != rec.status AND new_status != 'error' THEN
                        UPDATE driver_shifts 
                        SET status = new_status::shift_status, updated_at = CURRENT_TIMESTAMP 
                        WHERE id = rec.id;
                        
                        IF rec.status = 'scheduled' AND new_status = 'active' THEN 
                            count_activated := count_activated + 1; 
                        END IF;
                        IF rec.status = 'active' AND new_status = 'completed' THEN 
                            count_completed := count_completed + 1; 
                        END IF;
                    END IF;
                END LOOP;
                RAISE NOTICE 'Updated: % activated, % completed', count_activated, count_completed;
            END;
            $$ LANGUAGE plpgsql;
        `);
        
        console.log('✅ Auto-activation function created successfully');
        
        // Test it
        await pool.query('SELECT schedule_auto_activation()');
        console.log('✅ Auto-activation function tested successfully');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await pool.end();
    }
}

if (require.main === module) {
    createAutoActivation();
}

module.exports = { createAutoActivation };