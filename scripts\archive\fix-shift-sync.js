// ARCHIVED ON: 2025-08-08T12:14:58.420Z
// REASON: Unused script cleanup
// ORIGINAL PATH: scripts/fix-shift-sync.js

#!/usr/bin/env node

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER,
    host: process.env.DB_HOST,
    database: process.env.DB_NAME,
    password: process.env.DB_PASSWORD,
    port: process.env.DB_PORT,
});

async function fixShiftSync() {
    console.log('🔧 Fixing Shift Status Synchronization...\n');
    
    try {
        // Step 1: Check current problematic shifts
        const problemShifts = await pool.query(`
            SELECT id, truck_id, shift_type, status, start_date, end_date, start_time, end_time
            FROM driver_shifts 
            WHERE status = 'completed' AND end_date >= CURRENT_DATE
            ORDER BY truck_id, start_time
        `);
        
        console.log('🔍 Found incorrectly completed shifts:');
        problemShifts.rows.forEach(shift => {
            console.log(`   ID ${shift.id}: Truck ${shift.truck_id} (${shift.shift_type}) - ends ${shift.end_date.toISOString().substring(0,10)}`);
        });
        
        // Step 2: Reset these shifts to scheduled so they can be recalculated
        const resetResult = await pool.query(`
            UPDATE driver_shifts 
            SET status = 'scheduled', updated_at = CURRENT_TIMESTAMP 
            WHERE status = 'completed' AND end_date >= CURRENT_DATE
        `);
        
        console.log(`✅ Reset ${resetResult.rowCount} shifts to 'scheduled' status`);
        
        // Step 3: Run the corrected auto-activation to set proper statuses
        await pool.query('SELECT schedule_auto_activation()');
        console.log('✅ Ran auto-activation with corrected logic');
        
        // Step 4: Check the results
        const updatedShifts = await pool.query(`
            SELECT 
                id, 
                truck_id, 
                shift_type, 
                status, 
                start_date, 
                end_date, 
                start_time, 
                end_time,
                evaluate_shift_status(id, CURRENT_TIMESTAMP) as calculated_status
            FROM driver_shifts 
            WHERE id = ANY($1)
            ORDER BY truck_id, start_time
        `, [problemShifts.rows.map(s => s.id)]);
        
        console.log('\n📊 Updated Shift Status:');
        console.log('   ID  | Truck | Type  | Status    | Should Be | Time Range | Date Range');
        console.log('   ----|-------|-------|-----------|-----------|------------|------------');
        
        updatedShifts.rows.forEach(shift => {
            const statusMatch = shift.status === shift.calculated_status ? '✅' : '⚠️';
            const timeRange = `${shift.start_time.substring(0,5)}-${shift.end_time.substring(0,5)}`;
            const dateRange = shift.start_date.toISOString().substring(5,10) + ' to ' + shift.end_date.toISOString().substring(5,10);
            
            console.log(`   ${shift.id.toString().padStart(3)} | DT-${shift.truck_id.toString().padStart(3)} | ${shift.shift_type.padEnd(5)} | ${shift.status.padEnd(9)} | ${shift.calculated_status.padEnd(9)} ${statusMatch} | ${timeRange.padEnd(10)} | ${dateRange}`);
        });
        
        // Step 5: Show current time context
        const timeContext = await pool.query(`
            SELECT 
                CURRENT_DATE as current_date,
                CURRENT_TIME as current_time,
                CURRENT_TIMESTAMP as current_timestamp
        `);
        
        const ctx = timeContext.rows[0];
        console.log('\n🕐 Current Time Context:');
        console.log(`   Date: ${ctx.current_date.toISOString().substring(0,10)}`);
        console.log(`   Time: ${ctx.current_time.substring(0,8)}`);
        
        // Step 6: Verify the logic with a test
        console.log('\n🧪 Logic Verification:');
        const testResult = await pool.query(`
            SELECT 
                'Day shift at 12:00 should be active' as test,
                evaluate_shift_status(537, CURRENT_DATE + '12:00:00'::TIME) as result
            UNION ALL
            SELECT 
                'Night shift at 23:00 should be active' as test,
                evaluate_shift_status(536, CURRENT_DATE + '23:00:00'::TIME) as result
        `);
        
        testResult.rows.forEach(test => {
            console.log(`   ${test.test}: ${test.result}`);
        });
        
        console.log('\n🎉 Shift synchronization completed!');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await pool.end();
    }
}

if (require.main === module) {
    fixShiftSync();
}

module.exports = { fixShiftSync };